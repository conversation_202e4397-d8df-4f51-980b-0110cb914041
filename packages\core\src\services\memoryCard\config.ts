import { Schema } from "koishi";

/**
 * 信息捕获引擎配置
 */
export interface CaptureEngineConfig {
    /** 片段最大消息数 */
    maxMessagesPerSegment: number;
    /** 片段超时时间（分钟） */
    segmentTimeoutMinutes: number;
    /** 最小片段长度 */
    minSegmentLength: number;
}

/**
 * 记忆提炼引擎配置
 */
export interface RefineryEngineConfig {
    /** 是否启用自动提炼 */
    autoRefineEnabled: boolean;
    /** 提炼阈值（片段长度） */
    refineThreshold: number;
    /** 最大重要性分数 */
    maxImportanceScore: number;
}

/**
 * 情境触发器引擎配置
 */
export interface ContextTriggerEngineConfig {
    /** 是否启用触发器检查 */
    enabled: boolean;
    /** 检查间隔（毫秒） */
    checkIntervalMs: number;
    /** 最大触发结果数量 */
    maxTriggerResults: number;
}

/**
 * 长期追踪引擎配置
 */
export interface FollowUpEngineConfig {
    /** 是否启用后续追踪 */
    enabled: boolean;
    /** 检查间隔（分钟） */
    checkIntervalMinutes: number;
    /** 最大处理任务数量 */
    maxTasksPerCheck: number;
    /** 任务过期时间（天） */
    taskExpirationDays: number;
}

/**
 * 记忆卡片服务配置
 */
export interface MemoryCardServiceConfig {
    /** 是否启用服务 */
    enabled: boolean;
    /** 记忆卡片最大数量限制 */
    maxCardsPerUser: number;
    /** 自动清理间隔（小时） */
    cleanupIntervalHours: number;
    /** 记忆卡片重要性衰减因子 */
    importanceDecayFactor: number;
    /** 后续追踪任务检查间隔（分钟） */
    followUpCheckIntervalMinutes: number;
    /** 信息捕获引擎配置 */
    captureEngine: CaptureEngineConfig;
    /** 记忆提炼引擎配置 */
    refineryEngine: RefineryEngineConfig;
    /** 情境触发器引擎配置 */
    contextTriggerEngine: ContextTriggerEngineConfig;
    /** 长期追踪引擎配置 */
    followUpEngine: FollowUpEngineConfig;
}

export const MemoryCardServiceConfigSchema: Schema<MemoryCardServiceConfig> = Schema.object({
    enabled: Schema.boolean().default(true).description("是否启用记忆卡片服务"),

    maxCardsPerUser: Schema.number().default(1000).min(10).max(10000).description("每个用户最大记忆卡片数量"),

    cleanupIntervalHours: Schema.number().default(24).min(1).max(168).description("自动清理间隔（小时）"),

    importanceDecayFactor: Schema.number().default(0.95).min(0.1).max(1.0).description("记忆卡片重要性衰减因子"),

    followUpCheckIntervalMinutes: Schema.number()
        .default(60)
        .min(5)
        .max(1440)
        .description("后续追踪任务检查间隔（分钟）"),

    captureEngine: Schema.object({
        maxMessagesPerSegment: Schema.number().default(20).min(5).max(100).description("片段最大消息数"),

        segmentTimeoutMinutes: Schema.number().default(30).min(5).max(120).description("片段超时时间（分钟）"),

        minSegmentLength: Schema.number().default(3).min(1).max(10).description("最小片段长度"),
    }).description("信息捕获引擎配置"),

    refineryEngine: Schema.object({
        autoRefineEnabled: Schema.boolean().default(true).description("是否启用自动提炼"),

        refineThreshold: Schema.number().default(5).min(1).max(20).description("提炼阈值（片段长度）"),

        maxImportanceScore: Schema.number().default(1.0).min(0.1).max(1.0).description("最大重要性分数"),
    }).description("记忆提炼引擎配置"),

    contextTriggerEngine: Schema.object({
        enabled: Schema.boolean().default(true).description("是否启用触发器检查"),

        checkIntervalMs: Schema.number().default(1000).min(100).max(10000).description("检查间隔（毫秒）"),

        maxTriggerResults: Schema.number().default(10).min(1).max(50).description("最大触发结果数量"),
    }).description("情境触发器引擎配置"),

    followUpEngine: Schema.object({
        enabled: Schema.boolean().default(true).description("是否启用后续追踪"),

        checkIntervalMinutes: Schema.number().default(60).min(5).max(1440).description("检查间隔（分钟）"),

        maxTasksPerCheck: Schema.number().default(20).min(1).max(100).description("最大处理任务数量"),

        taskExpirationDays: Schema.number().default(30).min(1).max(365).description("任务过期时间（天）"),
    }).description("长期追踪引擎配置"),
});
