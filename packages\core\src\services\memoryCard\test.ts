/**
 * 记忆卡片系统测试文件
 *
 * 这个文件包含了记忆卡片系统的基本测试用例
 * 用于验证系统的核心功能是否正常工作
 */

import { MemoryCardServiceConfig } from "./config";
import { MemoryCard, UserMemory } from "./interfaces";
import { createDefaultUserMemory, generateCardId, isContextTriggerActive, isTimeAndContextTriggerActive, isTimeTriggerActive, validateMemoryCard } from "./utils";

/**
 * 创建测试用的MemoryCardService配置
 */
function createTestConfig(): MemoryCardServiceConfig {
    return {
        enabled: true,
        maxCardsPerUser: 100,
        cleanupIntervalHours: 24,
        importanceDecayFactor: 0.95,
        followUpCheckIntervalMinutes: 60,
        captureEngine: {
            maxMessagesPerSegment: 10,
            segmentTimeoutMinutes: 30,
            minSegmentLength: 3
        },
        refineryEngine: {
            autoRefineEnabled: true,
            refineThreshold: 5,
            maxImportanceScore: 1.0
        },
        contextTriggerEngine: {
            enabled: true,
            checkIntervalMs: 1000,
            maxTriggerResults: 10
        },
        followUpEngine: {
            enabled: true,
            checkIntervalMinutes: 60,
            maxTasksPerCheck: 20,
            taskExpirationDays: 30
        }
    };
}

/**
 * 创建测试用的记忆卡片
 */
function createTestMemoryCard(): MemoryCard {
    return {
        card_id: generateCardId(),
        type: "Fact",
        content: "用户喜欢喝咖啡",
        keywords: ["咖啡", "喜欢", "饮品"],
        source_message_ids: ["msg_001"],
        created_at: new Date().toISOString(),
        last_accessed: new Date().toISOString(),
        importance: 0.8,
        state: "Active"
    };
}

/**
 * 创建测试用的用户记忆
 */
function createTestUserMemory(): UserMemory {
    return createDefaultUserMemory("test_user_001", "测试用户");
}

/**
 * 测试工具函数
 */
async function testUtilityFunctions() {
    console.log("=== 测试工具函数 ===");

    // 测试ID生成
    const cardId = generateCardId();
    console.log(`生成的卡片ID: ${cardId}`);
    console.assert(cardId.startsWith("card_"), "卡片ID应该以'card_'开头");

    // 测试记忆卡片验证
    const validCard = createTestMemoryCard();
    const validationResult = validateMemoryCard(validCard);
    console.log(`记忆卡片验证结果: ${validationResult.success}`);
    console.assert(validationResult.success, "有效的记忆卡片应该通过验证");

    // 测试无效记忆卡片验证
    const invalidCard = { ...validCard, importance: 2.0 }; // 无效的重要性分数
    const invalidValidationResult = validateMemoryCard(invalidCard);
    console.log(`无效记忆卡片验证结果: ${invalidValidationResult.success}`);
    console.assert(!invalidValidationResult.success, "无效的记忆卡片应该验证失败");

    // 测试默认用户记忆创建
    const userMemory = createTestUserMemory();
    console.log(`创建的用户记忆: ${JSON.stringify(userMemory, null, 2)}`);
    console.assert(userMemory.user_id === "test_user_001", "用户ID应该正确设置");
    console.assert(userMemory.memory_cards.length === 0, "新用户应该没有记忆卡片");

    console.log("✅ 工具函数测试完成\n");
}

/**
 * 测试触发器函数
 */
async function testTriggerFunctions() {
    console.log("=== 测试触发器函数 ===");

    // 测试时间触发器
    const timeTrigger = {
        type: "Time" as const,
        startDate: new Date(Date.now() - 1000).toISOString(), // 1秒前
        endDate: new Date(Date.now() + 1000).toISOString(), // 1秒后
        description: "测试时间触发器"
    };

    const timeActive = isTimeTriggerActive(timeTrigger);
    console.log(`时间触发器激活状态: ${timeActive}`);
    console.assert(timeActive, "当前时间应该在触发器时间范围内");

    // 测试上下文触发器
    const contextTrigger = {
        type: "Context" as const,
        contextKeywords: ["咖啡", "喝"],
        description: "测试上下文触发器"
    };

    const contextActive = isContextTriggerActive(contextTrigger, "我想喝咖啡");
    console.log(`上下文触发器激活状态: ${contextActive}`);
    console.assert(contextActive, "包含关键词的消息应该激活上下文触发器");

    const contextInactive = isContextTriggerActive(contextTrigger, "今天天气不错");
    console.log(`上下文触发器非激活状态: ${contextInactive}`);
    console.assert(!contextInactive, "不包含关键词的消息不应该激活上下文触发器");

    // 测试时间和上下文复合触发器
    const timeAndContextTrigger = {
        type: "TimeAndContext" as const,
        startDate: new Date(Date.now() - 1000).toISOString(),
        endDate: new Date(Date.now() + 1000).toISOString(),
        contextKeywords: ["咖啡"],
        description: "测试复合触发器"
    };

    const compositeActive = isTimeAndContextTriggerActive(timeAndContextTrigger, "我想喝咖啡");
    console.log(`复合触发器激活状态: ${compositeActive}`);
    console.assert(compositeActive, "时间和上下文条件都满足时应该激活复合触发器");

    console.log("✅ 触发器函数测试完成\n");
}

/**
 * 测试记忆卡片服务基础功能
 */
async function testMemoryCardServiceBasics() {
    console.log("=== 测试记忆卡片服务基础功能 ===");

    // 注意：这里只是模拟测试，实际测试需要真实的Koishi Context
    console.log("创建测试配置...");
    const config = createTestConfig();
    console.log(`配置创建成功: ${JSON.stringify(config, null, 2)}`);

    console.log("创建测试记忆卡片...");
    const testCard = createTestMemoryCard();
    console.log(`测试卡片: ${JSON.stringify(testCard, null, 2)}`);

    console.log("创建测试用户记忆...");
    const testUserMemory = createTestUserMemory();
    console.log(`测试用户记忆: ${JSON.stringify(testUserMemory, null, 2)}`);

    console.log("✅ 记忆卡片服务基础功能测试完成\n");
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log("🚀 开始运行记忆卡片系统测试\n");

    try {
        await testUtilityFunctions();
        await testTriggerFunctions();
        await testMemoryCardServiceBasics();

        console.log("🎉 所有测试完成！");
    } catch (error) {
        console.error("❌ 测试过程中出现错误:", error);
    }
}

/**
 * 示例：如何使用记忆卡片系统
 */
async function demonstrateUsage() {
    console.log("\n=== 记忆卡片系统使用示例 ===");

    // 1. 创建用户记忆
    const userMemory = createDefaultUserMemory("demo_user", "演示用户");
    console.log("1. 创建用户记忆:", userMemory.profile.name);

    // 2. 创建记忆卡片
    const memoryCard = createTestMemoryCard();
    memoryCard.content = "用户提到明天要参加重要会议";
    memoryCard.type = "Event";
    memoryCard.keywords = ["会议", "明天", "重要"];

    // 添加触发器
    memoryCard.trigger = {
        type: "Time",
        startDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 明天
        description: "会议提醒"
    };

    // 添加后续追踪
    memoryCard.followUp = {
        type: "TimeDelayed",
        delayDays: 1,
        prompt: "询问会议进展如何"
    };

    console.log("2. 创建事件记忆卡片:", memoryCard.content);

    // 3. 验证卡片
    const validation = validateMemoryCard(memoryCard);
    console.log("3. 卡片验证结果:", validation.success);

    // 4. 模拟触发器检查
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
    const triggerActive = isTimeTriggerActive(memoryCard.trigger!, tomorrow);
    console.log("4. 明天触发器状态:", triggerActive);

    console.log("✅ 使用示例演示完成");
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests().then(() => {
        demonstrateUsage();
    });
}

export {
    createTestConfig,
    createTestMemoryCard,
    createTestUserMemory, demonstrateUsage, runAllTests, testMemoryCardServiceBasics, testTriggerFunctions, testUtilityFunctions
};

